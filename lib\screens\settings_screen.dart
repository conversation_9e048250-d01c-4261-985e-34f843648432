import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:convert';

// تم حذف الاستيرادات غير المستخدمة مؤقتاً
import '../services/local_notification_service.dart';
import '../services/notification_scheduler_service.dart';
import '../models/notification_schedule.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _autoBackup = false;

  String _appVersion = '';
  final NotificationSchedulerService _schedulerService =
      NotificationSchedulerService();
  final List<NotificationSchedule> _schedules = [];

  // عداد للمعرفات الآمنة
  static int _notificationIdCounter = 5000;

  // دالة لتوليد معرف آمن للإشعارات (32-bit integer)
  int _generateSafeNotificationId() {
    _notificationIdCounter++;
    if (_notificationIdCounter > 2147483647) {
      _notificationIdCounter = 5000; // إعادة تعيين العداد
    }
    return _notificationIdCounter;
  }

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadAppVersion();
    _initializeScheduler();
  }

  // تهيئة خدمة الجدولة
  Future<void> _initializeScheduler() async {
    try {
      await _schedulerService.initialize();
      setState(() {
        _schedules.clear();
        _schedules.addAll(_schedulerService.schedules);
      });
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الجدولة: $e');
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _notificationsEnabled = prefs.getBool('notifications') ?? true;
      _soundEnabled = prefs.getBool('sound') ?? true;
      _vibrationEnabled = prefs.getBool('vibration') ?? true;
      _autoBackup = prefs.getBool('auto_backup') ?? false;
    });
  }

  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = '${packageInfo.version} (${packageInfo.buildNumber})';
    });
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    }
  }

  // تحميل التنبيهات المحفوظة
  Future<void> _loadScheduledNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = prefs.getString('scheduled_notifications');
    if (notificationsJson != null) {
      final List<dynamic> notificationsList = jsonDecode(notificationsJson);
      setState(() {
        _scheduledNotifications.clear();
        _scheduledNotifications.addAll(
          notificationsList
              .map((item) => Map<String, dynamic>.from(item))
              .toList(),
        );
      });

      // تم إزالة جدولة التنبيهات المعقدة
    }
  }

  // حفظ التنبيهات
  Future<void> _saveScheduledNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final notificationsJson = jsonEncode(_scheduledNotifications);
    await prefs.setString('scheduled_notifications', notificationsJson);
  }

  // نظام تنبيهات بسيط - إرسال تنبيهات فورية
  Future<void> _sendInstantNotification(String type) async {
    try {
      await LocalNotificationService().initialize();

      String title = '';

      switch (type) {
        case 'overdue':
          title = '🔴 تنبيه ديون متأخرة';
          break;
        case 'due_today':
          title = '🟠 تنبيه ديون مستحقة اليوم';
          break;
        case 'summary':
          title = '📊 ملخص الديون';
          break;
        default:
          title = '🔔 تنبيه عام';
      }

      await LocalNotificationService().testNotification();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال $title'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في إرسال التنبيه: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إرسال التنبيه'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // عرض خيارات التنبيهات البسيطة
  void _showSimpleNotificationOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنبيهات بسيطة'),
        content: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.warning, color: Colors.red),
              title: const Text('ديون متأخرة'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('overdue');
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule, color: Colors.orange),
              title: const Text('ديون مستحقة اليوم'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('due_today');
              },
            ),
            ListTile(
              leading: const Icon(Icons.summarize, color: Colors.blue),
              title: const Text('ملخص الديون'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('summary');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // عرض حوار جدولة التنبيهات
  void _showScheduledNotificationsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.schedule,
                      color: Colors.blue.shade600,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'جدولة التنبيهات',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // قائمة التنبيهات
              Expanded(
                child: _schedules.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد تنبيهات مجدولة',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _schedules.length,
                        itemBuilder: (context, index) {
                          final schedule = _schedules[index];
                          return _buildScheduleCard(schedule);
                        },
                      ),
              ),

              const SizedBox(height: 16),

              // أزرار العمل
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showAddScheduleDialog(),
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة تنبيه'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.shade600,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed:
                              _schedules.where((s) => s.isEnabled).isEmpty
                                  ? null
                                  : () => _testScheduledNotifications(),
                          icon: const Icon(Icons.bug_report),
                          label: const Text('اختبار التنبيهات المفعلة'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.orange.shade600,
                            side: BorderSide(color: Colors.orange.shade300),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء كارت الجدولة
  Widget _buildScheduleCard(NotificationSchedule schedule) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: schedule.isEnabled ? Colors.white : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              schedule.isEnabled ? Colors.blue.shade200 : Colors.grey.shade300,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                _getScheduleIcon(schedule.type),
                color: schedule.isEnabled ? Colors.blue.shade600 : Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  schedule.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: schedule.isEnabled ? Colors.black87 : Colors.grey,
                  ),
                ),
              ),
              Switch(
                value: schedule.isEnabled,
                onChanged: (value) async {
                  await _schedulerService.toggleSchedule(schedule.id, value);
                  await _refreshSchedules();
                },
                activeColor: Colors.blue.shade600,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                schedule.time.format(context),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.repeat,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                _getWeekdaysText(schedule.weekdays),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            schedule.type.displayName,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: () => _showEditScheduleDialog(schedule),
                icon: const Icon(Icons.edit, size: 16),
                label: const Text('تعديل'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue.shade600,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
              TextButton.icon(
                onPressed: () => _deleteSchedule(schedule),
                icon: const Icon(Icons.delete, size: 16),
                label: const Text('حذف'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red.shade600,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // الحصول على أيقونة نوع الجدولة
  IconData _getScheduleIcon(NotificationScheduleType type) {
    switch (type) {
      case NotificationScheduleType.summary:
        return Icons.summarize;
      case NotificationScheduleType.overdue:
        return Icons.warning;
      case NotificationScheduleType.dueToday:
        return Icons.today;
      case NotificationScheduleType.dueSoon:
        return Icons.schedule;
      case NotificationScheduleType.custom:
        return Icons.notifications;
      default:
        return Icons.alarm;
    }
  }

  // تحويل أيام الأسبوع إلى نص
  String _getWeekdaysText(List<int> weekdays) {
    if (weekdays.length == 7) return 'يومياً';
    if (weekdays.length == 5 &&
        !weekdays.contains(6) &&
        !weekdays.contains(7)) {
      return 'أيام العمل';
    }
    if (weekdays.length == 2 && weekdays.contains(6) && weekdays.contains(7)) {
      return 'نهاية الأسبوع';
    }

    final dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return weekdays.map((day) => dayNames[day - 1]).join(', ');
  }

  // تحديث قائمة الجدولة
  Future<void> _refreshSchedules() async {
    setState(() {
      _schedules.clear();
      _schedules.addAll(_schedulerService.schedules);
    });
  }

  // اختبار التنبيهات المجدولة
  Future<void> _testScheduledNotifications() async {
    try {
      await _schedulerService.testScheduledNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم إرسال تنبيهات الاختبار بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في اختبار التنبيهات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  // حذف جدولة
  Future<void> _deleteSchedule(NotificationSchedule schedule) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف "${schedule.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _schedulerService.deleteSchedule(schedule.id);
      await _refreshSchedules();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف "${schedule.name}"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // عرض حوار إضافة جدولة جديدة
  void _showAddScheduleDialog() {
    _showScheduleFormDialog(null);
  }

  // عرض حوار تعديل جدولة
  void _showEditScheduleDialog(NotificationSchedule schedule) {
    _showScheduleFormDialog(schedule);
  }

  // عرض نموذج الجدولة (إضافة أو تعديل)
  void _showScheduleFormDialog(NotificationSchedule? existingSchedule) {
    // إنشاء controllers للحقول
    final nameController =
        TextEditingController(text: existingSchedule?.name ?? '');
    final customMessageController =
        TextEditingController(text: existingSchedule?.customMessage ?? '');

    TimeOfDay selectedTime =
        existingSchedule?.time ?? const TimeOfDay(hour: 9, minute: 0);
    NotificationScheduleType selectedType =
        existingSchedule?.type ?? NotificationScheduleType.summary;
    final List<int> selectedWeekdays =
        List.from(existingSchedule?.weekdays ?? [1, 2, 3, 4, 5, 6, 7]);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(
            existingSchedule == null ? 'إضافة تنبيه جديد' : 'تعديل التنبيه',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: IntrinsicHeight(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم التنبيه
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: TextField(
                        controller: nameController,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'اسم التنبيه',
                          labelStyle: const TextStyle(
                            color: Colors.black54,
                            fontSize: 14,
                          ),
                          hintText: 'أدخل اسم التنبيه',
                          hintStyle: const TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide:
                                const BorderSide(color: Colors.blue, width: 2),
                          ),
                          prefixIcon: const Icon(Icons.label_outline,
                              color: Colors.blue),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16),
                        ),
                      ),
                    ),

                    // نوع التنبيه
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: DropdownButtonFormField<NotificationScheduleType>(
                        value: selectedType,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'نوع التنبيه',
                          labelStyle: const TextStyle(
                            color: Colors.black54,
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide:
                                const BorderSide(color: Colors.blue, width: 2),
                          ),
                          prefixIcon: const Icon(Icons.category_outlined,
                              color: Colors.blue),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16),
                        ),
                        items: NotificationScheduleType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(
                              type.displayName,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedType = value;
                            });
                          }
                        },
                      ),
                    ),

                    // الوقت
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Material(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () async {
                            final time = await showTimePicker(
                              context: context,
                              initialTime: selectedTime,
                            );
                            if (time != null) {
                              setDialogState(() {
                                selectedTime = time;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.access_time,
                                    color: Colors.blue),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'الوقت',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black54,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        selectedTime.format(context),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const Icon(Icons.edit, color: Colors.grey),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    // أيام الأسبوع
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أيام التكرار:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF2C3E50),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              for (int i = 1; i <= 7; i++)
                                FilterChip(
                                  label: Text(
                                    _getDayName(i),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: selectedWeekdays.contains(i)
                                          ? Colors.blue.shade700
                                          : Colors.black87,
                                    ),
                                  ),
                                  selected: selectedWeekdays.contains(i),
                                  onSelected: (selected) {
                                    setDialogState(() {
                                      if (selected) {
                                        selectedWeekdays.add(i);
                                      } else {
                                        selectedWeekdays.remove(i);
                                      }
                                    });
                                  },
                                  selectedColor: Colors.blue.shade100,
                                  checkmarkColor: Colors.blue.shade700,
                                  backgroundColor: Colors.grey.shade100,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(
                                      color: selectedWeekdays.contains(i)
                                          ? Colors.blue.shade300
                                          : Colors.grey.shade300,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // رسالة مخصصة (للتنبيهات المخصصة)
                    if (selectedType == NotificationScheduleType.custom) ...[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: TextField(
                          controller: customMessageController,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                          maxLines: 3,
                          decoration: InputDecoration(
                            labelText: 'رسالة مخصصة',
                            labelStyle: const TextStyle(
                              color: Colors.black54,
                              fontSize: 14,
                            ),
                            hintText: 'أدخل رسالة التنبيه المخصصة',
                            hintStyle: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                  color: Colors.blue, width: 2),
                            ),
                            prefixIcon: const Icon(Icons.message_outlined,
                                color: Colors.blue),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                customMessageController.dispose();
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال اسم التنبيه'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (selectedWeekdays.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى اختيار يوم واحد على الأقل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final schedule = NotificationSchedule(
                  id: existingSchedule?.id ?? 0,
                  name: nameController.text.trim(),
                  time: selectedTime,
                  weekdays: selectedWeekdays,
                  isEnabled: existingSchedule?.isEnabled ?? true,
                  type: selectedType,
                  customMessage: selectedType == NotificationScheduleType.custom
                      ? customMessageController.text.trim()
                      : null,
                );

                try {
                  if (existingSchedule == null) {
                    await _schedulerService.addSchedule(schedule);
                  } else {
                    await _schedulerService.updateSchedule(schedule);
                  }

                  await _refreshSchedules();

                  // تنظيف الـ controllers
                  nameController.dispose();
                  customMessageController.dispose();

                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(existingSchedule == null
                            ? 'تم إضافة التنبيه بنجاح'
                            : 'تم تحديث التنبيه بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(existingSchedule == null ? 'إضافة' : 'تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على اسم اليوم
  String _getDayName(int day) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return dayNames[day - 1];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: const Color(0xFF2A5298), // لون أزرق مميز
        foregroundColor: Colors.white,
        elevation: 8,
        centerTitle: false,
        shadowColor: const Color(0xFF2A5298).withValues(alpha: 0.3),
        titleTextStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(
          color: Colors.white,
          size: 24,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      body: ListView(
        children: [
          // قسم الحساب
          _buildSectionHeader('الحساب'),
          _buildSettingsTile(
            icon: Icons.person_outline,
            title: 'الملف الشخصي',
            subtitle: 'إدارة معلومات الحساب',
            onTap: () {
              _showProfileDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.security_outlined,
            title: 'الأمان والخصوصية',
            subtitle: 'كلمة المرور والحماية',
            onTap: () {
              _showSecurityDialog();
            },
          ),

          const SizedBox(height: 16),

          // قسم المظهر
          _buildSectionHeader('المظهر'),
          _buildSwitchTile(
            icon: Icons.dark_mode_outlined,
            title: 'الوضع الليلي',
            subtitle: 'تفعيل المظهر الداكن',
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
              _saveSetting('dark_mode', value);
            },
          ),

          _buildSettingsTile(
            icon: Icons.text_fields_outlined,
            title: 'إعدادات الخط',
            subtitle: 'تخصيص حجم ونوع الخط',
            onTap: () {
              // سيتم تنفيذ إعدادات الخط لاحقاً
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('إعدادات الخط قيد التطوير'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // قسم الإشعارات
          _buildSectionHeader('الإشعارات'),
          _buildSwitchTile(
            icon: Icons.notifications_outlined,
            title: 'الإشعارات',
            subtitle: 'تلقي التنبيهات والإشعارات',
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
              _saveSetting('notifications', value);
            },
          ),
          _buildSettingsTile(
            icon: Icons.notifications_active_outlined,
            title: 'تنبيهات بسيطة',
            subtitle: 'إرسال تنبيهات فورية للديون المهمة',
            onTap: () {
              _showSimpleNotificationOptions();
            },
          ),
          _buildSettingsTile(
            icon: Icons.schedule_outlined,
            title: 'جدولة التنبيهات',
            subtitle:
                'إدارة التنبيهات المجدولة (${_schedules.where((s) => s.isEnabled).length} مفعل)',
            onTap: () {
              _showScheduledNotificationsDialog();
            },
          ),
          _buildSwitchTile(
            icon: Icons.volume_up_outlined,
            title: 'الصوت',
            subtitle: 'تشغيل أصوات الإشعارات',
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
              });
              _saveSetting('sound', value);
            },
          ),
          _buildSwitchTile(
            icon: Icons.vibration_outlined,
            title: 'الاهتزاز',
            subtitle: 'تفعيل الاهتزاز للإشعارات',
            value: _vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
              _saveSetting('vibration', value);
            },
          ),
          _buildSettingsTile(
            icon: Icons.bug_report_outlined,
            title: 'اختبار التنبيهات',
            subtitle: 'اختبار عمل نظام التنبيهات',
            onTap: () async {
              try {
                await LocalNotificationService().testNotification();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إرسال تنبيه اختبار بنجاح!'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في إرسال التنبيه: $e'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              }
            },
          ),

          const SizedBox(height: 16),

          // قسم البيانات
          _buildSectionHeader('البيانات والنسخ الاحتياطي'),
          _buildSwitchTile(
            icon: Icons.backup_outlined,
            title: 'النسخ الاحتياطي التلقائي',
            subtitle: 'حفظ البيانات تلقائياً',
            value: _autoBackup,
            onChanged: (value) {
              setState(() {
                _autoBackup = value;
              });
              _saveSetting('auto_backup', value);
            },
          ),
          _buildSettingsTile(
            icon: Icons.cloud_upload_outlined,
            title: 'تصدير البيانات',
            subtitle: 'حفظ نسخة من البيانات',
            onTap: () {
              _showExportDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.cloud_download_outlined,
            title: 'استيراد البيانات',
            subtitle: 'استعادة البيانات من نسخة احتياطية',
            onTap: () {
              _showImportDialog();
            },
          ),

          const SizedBox(height: 16),

          // قسم المساعدة والدعم
          _buildSectionHeader('المساعدة والدعم'),
          _buildSettingsTile(
            icon: Icons.help_outline,
            title: 'مركز المساعدة',
            subtitle: 'الأسئلة الشائعة والدعم',
            onTap: () {
              _showHelpDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.feedback_outlined,
            title: 'إرسال ملاحظات',
            subtitle: 'شاركنا رأيك واقتراحاتك',
            onTap: () {
              _showFeedbackDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.star_outline,
            title: 'تقييم التطبيق',
            subtitle: 'قيم التطبيق في متجر التطبيقات',
            onTap: () {
              _rateApp();
            },
          ),

          const SizedBox(height: 16),

          // قسم حول التطبيق
          _buildSectionHeader('حول التطبيق'),
          _buildSettingsTile(
            icon: Icons.info_outline,
            title: 'معلومات التطبيق',
            subtitle: 'الإصدار $_appVersion',
            onTap: () {
              _showAboutDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.description_outlined,
            title: 'شروط الاستخدام',
            subtitle: 'اقرأ شروط وأحكام الاستخدام',
            onTap: () {
              _showTermsDialog();
            },
          ),
          _buildSettingsTile(
            icon: Icons.privacy_tip_outlined,
            title: 'سياسة الخصوصية',
            subtitle: 'كيف نحمي بياناتك',
            onTap: () {
              _showPrivacyDialog();
            },
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.blue[700],
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  // دالة لتحديد لون الأيقونة حسب النوع
  Color _getIconColor(IconData icon) {
    switch (icon) {
      case Icons.person_outline:
        return const Color(0xFF2196F3); // أزرق
      case Icons.security_outlined:
        return const Color(0xFF4CAF50); // أخضر
      case Icons.dark_mode_outlined:
        return const Color(0xFF9C27B0); // بنفسجي
      case Icons.text_fields_outlined:
        return const Color(0xFF607D8B); // رمادي مزرق
      case Icons.notifications_outlined:
      case Icons.notifications_active_outlined:
        return const Color(0xFFF44336); // أحمر
      case Icons.volume_up_outlined:
      case Icons.vibration_outlined:
        return const Color(0xFF3F51B5); // نيلي
      case Icons.bug_report_outlined:
        return const Color(0xFFFF5722); // برتقالي محمر
      case Icons.backup_outlined:
      case Icons.cloud_upload_outlined:
        return const Color(0xFF00BCD4); // سماوي
      case Icons.cloud_download_outlined:
        return const Color(0xFF9C27B0); // بنفسجي
      case Icons.help_outline:
        return const Color(0xFF009688); // تيل
      case Icons.feedback_outlined:
        return const Color(0xFF795548); // بني
      case Icons.star_outline:
        return const Color(0xFFFFC107); // ذهبي
      case Icons.info_outline:
        return const Color(0xFF2A5298); // أزرق داكن
      case Icons.description_outlined:
      case Icons.privacy_tip_outlined:
        return const Color(0xFF607D8B); // رمادي
      default:
        return const Color(0xFF2A5298); // اللون الافتراضي
    }
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final Color iconColor = _getIconColor(icon);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: iconColor.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Icon(icon, color: iconColor, size: 22),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            height: 1.2,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Color(0xFF2A5298),
        ),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final Color iconColor = _getIconColor(icon);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SwitchListTile(
        secondary: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: iconColor.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Icon(icon, color: iconColor, size: 22),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            height: 1.2,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF2A5298),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  void _showFeedbackDialog() {
    final TextEditingController feedbackController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'إرسال ملاحظات',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'نحن نقدر ملاحظاتك! شاركنا رأيك أو اقتراحاتك لتحسين التطبيق.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: feedbackController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'اكتب ملاحظاتك هنا...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Send feedback
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('شكراً لك! تم إرسال ملاحظاتك بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('إرسال'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'محاسب',
      applicationVersion: _appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.blue[600],
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(Icons.calculate, color: Colors.white, size: 32),
      ),
      children: [
        const SizedBox(height: 16),
        const Text(
          'تطبيق محاسب هو حل شامل لإدارة الديون والحسابات المالية. '
          'يساعدك على تتبع المبيعات والمدفوعات بطريقة سهلة ومنظمة.',
          style: TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 16),
        const Text(
          '© 2024 جميع الحقوق محفوظة',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  // دوال إضافية للأزرار
  void _showProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.person, color: Colors.blue[600]),
            const SizedBox(width: 8),
            const Text('الملف الشخصي'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم المستخدم: مدير النظام'),
            SizedBox(height: 8),
            Text('نوع الحساب: حساب محلي'),
            SizedBox(height: 8),
            Text('تاريخ الإنشاء: 2024'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showSecurityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.green[600]),
            const SizedBox(width: 8),
            const Text('الأمان والخصوصية'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('• البيانات محفوظة محلياً على الجهاز'),
            SizedBox(height: 8),
            Text('• لا يتم مشاركة البيانات مع أطراف خارجية'),
            SizedBox(height: 8),
            Text('• يمكنك إنشاء نسخ احتياطية آمنة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.cloud_upload, color: Colors.orange[600]),
            const SizedBox(width: 8),
            const Text('تصدير البيانات'),
          ],
        ),
        content: const Text(
          'سيتم تصدير جميع البيانات (العملاء، الديون، المدفوعات) إلى ملف يمكن مشاركته أو حفظه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('جاري تصدير البيانات...'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.cloud_download, color: Colors.purple[600]),
            const SizedBox(width: 8),
            const Text('استيراد البيانات'),
          ],
        ),
        content: const Text(
          'اختر ملف النسخة الاحتياطية لاستعادة البيانات. سيتم استبدال البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('جاري اختيار الملف...'),
                  backgroundColor: Colors.purple,
                ),
              );
            },
            child: const Text('اختيار ملف'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.help, color: Colors.teal[600]),
            const SizedBox(width: 8),
            const Text('مركز المساعدة'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الأسئلة الشائعة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• كيف أضيف عميل جديد؟'),
              Text('  اضغط على زر + في الشاشة الرئيسية'),
              SizedBox(height: 8),
              Text('• كيف أسدد دين؟'),
              Text('  اضغط مطولاً على بطاقة الدين واختر تسديد'),
              SizedBox(height: 8),
              Text('• كيف أنشئ نسخة احتياطية؟'),
              Text('  من الإعدادات > تصدير البيانات'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _rateApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.star, color: Colors.amber[600]),
            const SizedBox(width: 8),
            const Text('تقييم التطبيق'),
          ],
        ),
        content: const Text(
          'هل تستمتع باستخدام تطبيق محاسب؟ نرجو منك تقييم التطبيق في متجر التطبيقات!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لاحقاً'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('شكراً لك! جاري فتح متجر التطبيقات...'),
                  backgroundColor: Colors.amber,
                ),
              );
            },
            child: const Text('تقييم الآن'),
          ),
        ],
      ),
    );
  }

  void _showTermsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('شروط الاستخدام'),
        content: const SingleChildScrollView(
          child: Text(
            '1. يُستخدم هذا التطبيق لإدارة الديون الشخصية فقط\n\n'
            '2. المستخدم مسؤول عن دقة البيانات المدخلة\n\n'
            '3. ننصح بإنشاء نسخ احتياطية دورية\n\n'
            '4. التطبيق لا يتطلب اتصال بالإنترنت\n\n'
            '5. جميع البيانات محفوظة محلياً على الجهاز',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('سياسة الخصوصية'),
        content: const SingleChildScrollView(
          child: Text(
            'نحن نحترم خصوصيتك:\n\n'
            '• لا نجمع أي بيانات شخصية\n'
            '• جميع البيانات محفوظة محلياً\n'
            '• لا نشارك المعلومات مع أطراف ثالثة\n'
            '• لا نتتبع استخدامك للتطبيق\n'
            '• يمكنك حذف جميع البيانات في أي وقت',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // قائمة التنبيهات المجدولة (قابلة للتعديل)
  final List<Map<String, dynamic>> _scheduledNotifications = [
    {
      'id': 1,
      'name': 'تنبيه صباحي',
      'time': '09:00',
      'days': 'يومياً',
      'type': 'ملخص يومي',
      'enabled': false,
      'status': 'متاح',
    },
    {
      'id': 2,
      'name': 'تنبيه مسائي',
      'time': '18:00',
      'days': 'الأحد - الخميس',
      'type': 'الديون المتأخرة',
      'enabled': true,
      'status': 'متاح',
    },
  ];

  // إضافة تنبيه جديد
  void _addNewNotification() {
    showDialog(
      context: context,
      builder: (context) => _buildAddNotificationDialog(),
    );
  }

  // حذف تنبيه - نسخة محسنة
  void _deleteNotification(int index) {
    if (index < 0 || index >= _scheduledNotifications.length) {
      debugPrint('❌ فهرس التنبيه غير صحيح: $index');
      return;
    }

    final notification = _scheduledNotifications[index];
    final notificationName = notification['name'] ?? 'تنبيه غير معروف';
    final notificationId = notification['id'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التنبيه'),
        content: Text('هل تريد حذف "$notificationName"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // إلغاء التنبيه المجدول أولاً
                await LocalNotificationService()
                    .cancelNotification(notificationId);

                // حذف التنبيه من القائمة
                setState(() {
                  _scheduledNotifications.removeAt(index);
                });

                // حفظ التغييرات
                await _saveScheduledNotifications();

                // إغلاق الحوار وإظهار رسالة نجاح
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حذف "$notificationName" بنجاح'),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }

                debugPrint('✅ تم حذف التنبيه: $notificationName');
              } catch (e) {
                debugPrint('❌ خطأ في حذف التنبيه: $e');
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('حدث خطأ أثناء حذف التنبيه'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  // بناء حوار إضافة تنبيه جديد
  Widget _buildAddNotificationDialog() {
    final nameController = TextEditingController();
    TimeOfDay selectedTime = TimeOfDay.now();
    String selectedType = 'ملخص يومي';
    String selectedDays = 'يومياً';

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_alarm,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text('إضافة تنبيه جديد'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // اسم التنبيه
                  TextField(
                    controller: nameController,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'اسم التنبيه',
                      labelStyle: TextStyle(color: Colors.grey),
                      border: OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // اختيار الوقت
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.white,
                    ),
                    child: ListTile(
                      title: const Text(
                        'الوقت',
                        style: TextStyle(color: Colors.black),
                      ),
                      subtitle: Text(
                        _formatTimeOfDay(selectedTime),
                        style: const TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      trailing: const Icon(
                        Icons.access_time,
                        color: Colors.grey,
                      ),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: selectedTime,
                        );
                        if (time != null) {
                          setDialogState(() {
                            selectedTime = time;
                          });
                        }
                      },
                    ),
                  ),

                  // نوع التنبيه
                  DropdownButtonFormField<String>(
                    value: selectedType,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'نوع التنبيه',
                      labelStyle: TextStyle(color: Colors.grey),
                      border: OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'ملخص يومي', child: Text('ملخص يومي')),
                      DropdownMenuItem(
                          value: 'الديون المتأخرة',
                          child: Text('الديون المتأخرة')),
                      DropdownMenuItem(
                          value: 'الديون المستحقة',
                          child: Text('الديون المستحقة')),
                      DropdownMenuItem(
                          value: 'تذكير عام', child: Text('تذكير عام')),
                    ],
                    onChanged: (value) {
                      setDialogState(() {
                        selectedType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // الأيام
                  DropdownButtonFormField<String>(
                    value: selectedDays,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'الأيام',
                      labelStyle: TextStyle(color: Colors.grey),
                      border: OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: const [
                      DropdownMenuItem(value: 'يومياً', child: Text('يومياً')),
                      DropdownMenuItem(
                          value: 'الأحد - الخميس',
                          child: Text('الأحد - الخميس')),
                      DropdownMenuItem(
                          value: 'نهاية الأسبوع', child: Text('نهاية الأسبوع')),
                      DropdownMenuItem(value: 'مخصص', child: Text('مخصص')),
                    ],
                    onChanged: (value) {
                      setDialogState(() {
                        selectedDays = value!;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال اسم التنبيه'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // إنشاء تنبيه جديد
                final newNotification = {
                  'id':
                      _generateSafeNotificationId(), // استخدام دالة آمنة لتوليد المعرف
                  'name': nameController.text.trim(),
                  'time':
                      '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}',
                  'days': selectedDays,
                  'type': selectedType,
                  'enabled': true,
                  'status': 'متاح',
                };

                setState(() {
                  _scheduledNotifications.add(newNotification);
                });

                await _saveScheduledNotifications();
                // تم إزالة جدولة التنبيهات المعقدة

                // طباعة للتحقق
                print('تم إضافة تنبيه جديد: ${newNotification['name']}');
                print(
                    'عدد التنبيهات الحالي: ${_scheduledNotifications.length}');

                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          'تم إضافة "${nameController.text.trim()}" بنجاح (${_scheduledNotifications.length} تنبيهات)'),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  // بناء قائمة التنبيهات المجدولة
  Widget _buildScheduledNotificationsList() {
    if (_scheduledNotifications.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: const Row(
          children: [
            Icon(Icons.info_outline, color: Colors.grey, size: 18),
            SizedBox(width: 8),
            Text(
              'لا توجد تنبيهات مجدولة',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return StatefulBuilder(
      builder: (context, setListState) {
        return Column(
          children: _scheduledNotifications.asMap().entries.map((entry) {
            final index = entry.key;
            final notification = entry.value;
            return StatefulBuilder(
              builder: (context, setCardState) {
                return Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: notification['enabled']
                        ? Colors.purple.shade50
                        : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: notification['enabled']
                          ? Colors.purple.shade300
                          : Colors.grey.shade300,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: notification['enabled']
                            ? Colors.purple.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // الصف العلوي: الأيقونة والاسم والمفتاح
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: notification['enabled']
                                  ? Colors.purple.shade100
                                  : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              notification['enabled']
                                  ? Icons.alarm_on
                                  : Icons.alarm_off,
                              color: notification['enabled']
                                  ? Colors.purple.shade600
                                  : Colors.grey,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  notification['name'],
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: notification['enabled']
                                        ? const Color(0xFF2C3E50)
                                        : Colors.grey,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${notification['time']} • ${notification['days']}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: notification['enabled']
                                        ? Colors.purple.shade600
                                        : Colors.grey,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                const SizedBox(height: 4),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 4,
                                  children: [
                                    Text(
                                      notification['type'],
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: notification['enabled']
                                            ? const Color(0xFF7F8C8D)
                                            : Colors.grey,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: notification['status'] == 'متاح'
                                            ? Colors.green.shade100
                                            : Colors.orange.shade100,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color:
                                              notification['status'] == 'متاح'
                                                  ? Colors.green.shade300
                                                  : Colors.orange.shade300,
                                        ),
                                      ),
                                      child: Text(
                                        notification['status'] ?? 'متاح',
                                        style: TextStyle(
                                          fontSize: 9,
                                          fontWeight: FontWeight.bold,
                                          color:
                                              notification['status'] == 'متاح'
                                                  ? Colors.green.shade700
                                                  : Colors.orange.shade700,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          StatefulBuilder(
                            builder: (context, setNotificationState) {
                              return Switch(
                                value: notification['enabled'],
                                onChanged: (value) async {
                                  setNotificationState(() {
                                    notification['enabled'] = value;
                                  });
                                  setState(() {
                                    notification['enabled'] = value;
                                  });

                                  // حفظ التنبيهات
                                  await _saveScheduledNotifications();

                                  if (value) {
                                    // تم إزالة جدولة التنبيهات المعقدة

                                    // إظهار رسالة تأكيد التفعيل
                                    if (mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'تم تفعيل ${notification['name']} في ${notification['time']}',
                                          ),
                                          backgroundColor: Colors.green,
                                          duration: const Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  } else {
                                    // إلغاء التنبيه
                                    await LocalNotificationService()
                                        .cancelNotification(notification['id']);

                                    if (mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'تم إلغاء ${notification['name']}',
                                          ),
                                          backgroundColor: Colors.orange,
                                          duration: const Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  }
                                },
                                activeColor: Colors.purple,
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // أزرار التعديل والحذف
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                _editScheduledNotification(notification);
                              },
                              icon: const Icon(Icons.edit, size: 16),
                              label: const Text('تعديل'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.purple.shade600,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 10,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                _deleteNotification(index);
                              },
                              icon: const Icon(Icons.delete, size: 16),
                              label: const Text('حذف'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.shade600,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 10,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            );
          }).toList(),
        );
      },
    );
  }

  // حوار إضافة تنبيه مجدول
  void _showScheduleNotificationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.purple, Colors.purple.shade600],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.add_alarm, color: Colors.white, size: 20),
            ),
            const SizedBox(width: 12),
            const Text(
              'إضافة تنبيه مجدول',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        content: const Text(
          'يمكنك الآن:\n• تعديل أوقات التنبيهات الموجودة\n• تفعيل/إلغاء التنبيهات\n• التشغيل في الخلفية\n\nلإضافة تنبيهات جديدة، استخدم زر التعديل للتنبيهات الموجودة.',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  // تعديل تنبيه مجدول
  void _editScheduledNotification(Map<String, dynamic> notification) {
    TimeOfDay selectedTime = TimeOfDay(
      hour: int.parse(notification['time'].split(':')[0]),
      minute: int.parse(notification['time'].split(':')[1]),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.purple, Colors.purple.shade600],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.schedule, color: Colors.white, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              'تعديل ${notification['name']}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.purple.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.purple.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            color: Colors.purple.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'الوقت المحدد:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      GestureDetector(
                        onTap: () async {
                          final TimeOfDay? picked = await showTimePicker(
                            context: context,
                            initialTime: selectedTime,
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: ColorScheme.light(
                                    primary: Colors.purple.shade600,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (picked != null) {
                            setDialogState(() {
                              selectedTime = picked;
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.purple.shade300),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.schedule,
                                color: Colors.purple.shade600,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _formatTimeOfDay(selectedTime),
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue.shade600,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'سيتم تفعيل التنبيه في الوقت المحدد يومياً',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF2C3E50),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              setState(() {
                notification['time'] =
                    '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
              });

              // حفظ التنبيهات
              await _saveScheduledNotifications();

              // تم إزالة جدولة التنبيهات المعقدة

              if (mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم تحديث ${notification['name']} إلى ${_formatTimeOfDay(selectedTime)}',
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // تنسيق الوقت
  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute;
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'ظهراً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  // تم حذف نظام التنبيهات المعقد واستبداله بنظام بسيط

  Widget buildSubNotificationOption({
    required String text,
    required bool value,
    required Future<void> Function(bool) onChanged,
    required Color color,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 13, color: Color(0xFF34495E)),
          ),
        ),
        Transform.scale(
          scale: 0.8,
          child: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: color,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  Widget buildFontSection({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }
}
