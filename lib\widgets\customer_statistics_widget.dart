import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/customer_statistics.dart';
import '../utils/number_formatter.dart';

class CustomerStatisticsWidget extends StatelessWidget {
  const CustomerStatisticsWidget({
    super.key,
    required this.statistics,
    required this.isExpanded,
    required this.onToggle,
  });
  final CustomerStatistics statistics;
  final bool isExpanded;
  final VoidCallback onToggle;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الإحصائيات
          _buildStatisticsHeader(),

          // الإحصائيات المفصلة
          if (isExpanded) _buildDetailedStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatisticsHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        border: Border(bottom: BorderSide(color: Colors.grey.shade100)),
      ),
      child: Row(
        children: [
          // أيقونة الإحصائيات مع خلفية دائرية
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.analytics_outlined,
              color: Colors.blue.shade600,
              size: 18,
            ),
          ),

          const SizedBox(width: 12),

          // معلومات أساسية
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إحصائيات العميل',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'تفاصيل نشاط العميل',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // زر التوسيع
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: isExpanded ? Colors.orange.shade50 : Colors.grey.shade50,
              borderRadius: BorderRadius.circular(6),
            ),
            child: IconButton(
              onPressed: onToggle,
              icon: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up_rounded
                    : Icons.keyboard_arrow_down_rounded,
                color:
                    isExpanded ? Colors.orange.shade600 : Colors.grey.shade600,
                size: 20,
              ),
              tooltip: isExpanded ? 'إخفاء التفاصيل' : 'عرض التفاصيل',
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatistics() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // إجمالي الديون
          _buildTotalDebtsCard(),

          const SizedBox(height: 16),

          // المبيعات
          _buildSectionTitle(
            'المبيعات',
            Icons.trending_up_rounded,
            Colors.blue,
          ),
          const SizedBox(height: 8),
          _buildSalesGrid(),

          const SizedBox(height: 16),

          // المدفوعات
          _buildSectionTitle(
            'المدفوعات',
            Icons.account_balance_wallet_rounded,
            Colors.green,
          ),
          const SizedBox(height: 8),
          _buildPaymentsGrid(),

          const SizedBox(height: 16),

          // المستحقات والمتأخرات
          _buildSectionTitle(
            'المستحقات',
            Icons.schedule_rounded,
            Colors.orange,
          ),
          const SizedBox(height: 8),
          _buildDuesGrid(),

          const SizedBox(height: 16),

          // الكميات حسب النوع
          if (statistics.cardTypeQuantities.isNotEmpty) ...[
            _buildSectionTitle(
              'الكميات حسب النوع',
              Icons.inventory_2_rounded,
              Colors.purple,
            ),
            const SizedBox(height: 8),
            _buildCardTypesGrid(),
            const SizedBox(height: 16),
          ],

          // نشاط العميل وسلوك السداد
          _buildCustomerBehavior(),

          const SizedBox(height: 16),

          // آخر عملية تسديد
          if (statistics.lastPaymentDate != null) _buildLastPayment(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 10),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalDebtsCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade50, Colors.orange.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة مصغر
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade400, Colors.orange.shade600],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withValues(alpha: 0.3),
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.account_balance_wallet_rounded,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الديون',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    Text(
                      'المبلغ المستحق والكارتات',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // معلومات الديون مصغرة
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade200, width: 0.5),
            ),
            child: Row(
              children: [
                // المبلغ الإجمالي
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 16,
                        alignment: Alignment.center,
                        child: Text(
                          'المبلغ',
                          style: TextStyle(
                            fontSize: 9,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        _formatDebtAmountText(statistics.totalDebtsAmount),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // خط فاصل
                Container(height: 35, width: 1, color: Colors.orange.shade200),

                // عدد الكارتات
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 16,
                        alignment: Alignment.center,
                        child: Text(
                          'الكارتات',
                          style: TextStyle(
                            fontSize: 9,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        _formatDebtCardsCountText(statistics.totalCardsCount),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // خط فاصل
                Container(height: 35, width: 1, color: Colors.orange.shade200),

                // عدد الديون
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 16,
                        alignment: Alignment.center,
                        child: Text(
                          'العمليات',
                          style: TextStyle(
                            fontSize: 9,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        '${statistics.paymentCounter}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesGrid() {
    return Column(
      children: [
        _buildStatCard(
          'اليوم',
          NumberFormatter.formatCurrency(statistics.todaySalesAmount),
          '${statistics.todaySalesCount} كارت',
          Colors.blue,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          'أمس',
          NumberFormatter.formatCurrency(statistics.yesterdaySalesAmount),
          '${statistics.yesterdaySalesCount} كارت',
          Colors.blue.shade300,
        ),
      ],
    );
  }

  Widget _buildPaymentsGrid() {
    return Column(
      children: [
        _buildStatCard(
          'اليوم',
          NumberFormatter.formatCurrency(statistics.paidTodayAmount),
          '${statistics.paidTodayCount} عملية',
          Colors.green,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          'أمس',
          NumberFormatter.formatCurrency(statistics.paidYesterdayAmount),
          '${statistics.paidYesterdayCount} عملية',
          Colors.green.shade300,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          'هذا الأسبوع',
          NumberFormatter.formatCurrency(statistics.paidWeekAmount),
          '${statistics.paidWeekCount} عملية',
          Colors.green.shade400,
        ),
      ],
    );
  }

  Widget _buildDuesGrid() {
    return Column(
      children: [
        _buildStatCard(
          'مستحق اليوم',
          NumberFormatter.formatCurrency(statistics.dueTodayAmount),
          '${statistics.dueTodayCount} كارت',
          Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          'قريب الاستحقاق',
          NumberFormatter.formatCurrency(statistics.dueNearAmount),
          '${statistics.dueNearCount} كارت',
          Colors.orange.shade300,
        ),
        const SizedBox(height: 8),
        _buildOverdueCard(),
      ],
    );
  }

  Widget _buildCardTypesGrid() {
    final cardTypes = statistics.cardTypeQuantities.values.toList();

    return Column(
      children: cardTypes.map((cardType) {
        return Container(
          margin: const EdgeInsets.only(bottom: 6),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.teal.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.teal.shade200, width: 0.5),
                ),
                child: Icon(
                  _getCardTypeIcon(cardType.cardType),
                  color: _getCardTypeColor(cardType.cardType),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      cardType.cardType,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'الكمية ${cardType.quantity}',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Text(
                NumberFormatter.formatCurrency(cardType.amount),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCustomerBehavior() {
    return Row(
      children: [
        Expanded(
          child: _buildBehaviorCard(
            'النشاط',
            statistics.activity.displayName,
            statistics.activity.color,
            Icons.psychology_rounded,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildBehaviorCard(
            'السداد',
            statistics.paymentBehavior.displayName,
            statistics.paymentBehavior.color,
            Icons.timer_rounded,
          ),
        ),
      ],
    );
  }

  Widget _buildOverdueCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Row(
            children: [
              const Icon(Icons.error_rounded, color: Colors.red, size: 14),
              const SizedBox(width: 6),
              Text(
                'متأخر',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),

          // المبلغ الإجمالي
          Text(
            NumberFormatter.formatCurrency(statistics.overdueDebtsAmount),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 2),

          // العدد الإجمالي
          Text(
            '${statistics.overdueDebtsCount} كارت',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),

          // تفاصيل أنواع الكارتات المتأخرة
          if (statistics.overdueCardTypes.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200, width: 0.5),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل الأنواع:',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  ...statistics.overdueCardTypes.values.map((cardType) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 2),
                      child: Row(
                        children: [
                          Icon(
                            _getCardTypeIcon(cardType.cardType),
                            color: _getCardTypeColor(cardType.cardType),
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              cardType.cardType,
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Text(
                            '${cardType.quantity}',
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.red.shade600,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBehaviorCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastPayment() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.teal.shade50,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.teal.shade200),
                ),
                child: const Icon(
                  Icons.receipt_long_rounded,
                  color: Colors.teal,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'آخر عملية تسديد',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'تفاصيل آخر دفعة مسجلة',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // بطاقة عدد الكارتات والمبلغ المسدد
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.teal.shade50, Colors.teal.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.teal.shade200),
              boxShadow: [
                BoxShadow(
                  color: Colors.teal.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // أيقونة المدفوعات
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.teal.shade400, Colors.teal.shade600],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.teal.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.paid_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات الكارتات والمبلغ
                Expanded(
                  child: Row(
                    children: [
                      // عدد الكارتات
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الكارتات المسددة',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatPaidCardsCountText(
                                statistics.totalPaidCardsCount,
                              ),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // خط فاصل
                      Container(
                        height: 40,
                        width: 1,
                        color: Colors.teal.shade200,
                      ),

                      const SizedBox(width: 16),

                      // المبلغ الإجمالي
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المبلغ الإجمالي',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatPaidAmountText(statistics.totalPaidAmount),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // تفاصيل التاريخ والوقت والإحصائيات
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                // الصف الأول: التاريخ والوقت
                Row(
                  children: [
                    // التاريخ
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.blue.shade200,
                            width: 0.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.calendar_today_rounded,
                              color: Colors.blue,
                              size: 16,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'التاريخ',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              statistics.lastPaymentDate != null
                                  ? _formatFullDate(statistics.lastPaymentDate!)
                                  : 'غير محدد',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // الوقت
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.orange.shade200,
                            width: 0.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.access_time_rounded,
                              color: Colors.orange,
                              size: 16,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'الوقت',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              statistics.lastPaymentTime ?? 'غير محدد',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // الصف الثاني: المدة الزمنية والإحصائيات
                Row(
                  children: [
                    // المدة الزمنية
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.teal.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.teal.shade200,
                            width: 0.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.schedule_rounded,
                              color: Colors.teal,
                              size: 16,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'المدة الزمنية',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _formatTimeSinceLastPayment(
                                statistics.lastPaymentDate,
                              ),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.teal.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // إحصائيات المدفوعات
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.green.shade200,
                            width: 0.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.analytics_rounded,
                              color: Colors.green,
                              size: 16,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'إجمالي العمليات',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${statistics.paymentCounter} عملية',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة لتنسيق التاريخ الكامل
  String _formatFullDate(DateTime date) {
    final arabicMonths = [
      'الشهر الأول',
      'الشهر الثاني',
      'الشهر الثالث',
      'الشهر الرابع',
      'الشهر الخامس',
      'الشهر السادس',
      'الشهر السابع',
      'الشهر الثامن',
      'الشهر التاسع',
      'الشهر العاشر',
      'الشهر الحادي عشر',
      'الشهر الثاني عشر',
    ];

    final arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    final dayName = arabicDays[date.weekday - 1];
    final monthName = arabicMonths[date.month - 1];

    return '$dayName ${date.day} $monthName ${date.year}';
  }

  // دالة لتنسيق عدد الكارتات المسددة (للنص العادي)
  String _formatPaidCardsCountText(int count) {
    if (count == 0) {
      return 'لا توجد كارتات';
    } else if (count == 1) {
      return 'كارت واحد';
    } else if (count == 2) {
      return 'كارتان';
    } else if (count <= 10) {
      return '$count كارتات';
    } else if (count <= 100) {
      return '$count كارت';
    } else {
      return '+100 كارت';
    }
  }

  // دالة لتنسيق المبلغ المسدد (للنص العادي)
  String _formatPaidAmountText(double amount) {
    if (amount == 0) {
      return '0 د.ع';
    } else if (amount < 1000) {
      return '${amount.toInt()} د.ع';
    } else if (amount < 10000) {
      final thousands = (amount / 1000).toStringAsFixed(1);
      return '$thousandsألف د.ع';
    } else if (amount < 100000) {
      final thousands = (amount / 1000).toInt();
      return '$thousandsألف د.ع';
    } else {
      final thousands = (amount / 1000).toInt();
      return '+$thousandsألف د.ع';
    }
  }

  // دالة لتنسيق الوقت منذ آخر تسديد مثل "منذ 1 يوم"
  String _formatTimeSinceLastPayment(DateTime? lastPaymentDate) {
    if (lastPaymentDate == null) {
      return 'لا توجد\nمدفوعات';
    }

    final now = DateTime.now();
    final difference = now.difference(lastPaymentDate);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'منذ لحظات';
        } else if (difference.inMinutes == 1) {
          return 'منذ دقيقة';
        } else {
          return 'منذ ${difference.inMinutes}\nدقيقة';
        }
      } else if (difference.inHours == 1) {
        return 'منذ ساعة';
      } else {
        return 'منذ ${difference.inHours}\nساعة';
      }
    } else if (difference.inDays == 1) {
      return 'منذ يوم';
    } else if (difference.inDays == 2) {
      return 'منذ يومين';
    } else if (difference.inDays <= 10) {
      return 'منذ ${difference.inDays}\nأيام';
    } else if (difference.inDays <= 30) {
      return 'منذ ${difference.inDays}\nيوم';
    } else if (difference.inDays <= 60) {
      final months = (difference.inDays / 30).round();
      return 'منذ $months\nشهر';
    } else {
      final months = (difference.inDays / 30).round();
      return 'منذ $months\nشهر';
    }
  }

  Widget _buildStatCard(
    String title,
    String amount,
    String count,
    Color color,
  ) {
    // تحديد الأيقونة حسب العنوان
    IconData getIconForTitle(String title) {
      switch (title.toLowerCase()) {
        case 'اليوم':
          return Icons.today_rounded;
        case 'أمس':
          return Icons.history_rounded;
        case 'هذا الأسبوع':
          return Icons.date_range_rounded;
        case 'مستحق اليوم':
          return Icons.warning_rounded;
        case 'قريب الاستحقاق':
          return Icons.schedule_rounded;
        case 'متأخر':
          return Icons.error_rounded;
        default:
          return Icons.analytics_rounded;
      }
    }

    // ألوان حكومية مميزة
    Color getGovernmentColor(String title) {
      switch (title.toLowerCase()) {
        case 'اليوم':
          return const Color(0xFF1B4332); // أخضر حكومي داكن
        case 'أمس':
          return const Color(0xFF2D3748); // رمادي حكومي
        case 'هذا الأسبوع':
          return const Color(0xFF2C5282); // أزرق حكومي
        case 'مستحق اليوم':
          return const Color(0xFFD69E2E); // ذهبي حكومي
        case 'قريب الاستحقاق':
          return const Color(0xFFED8936); // برتقالي حكومي
        case 'متأخر':
          return const Color(0xFFE53E3E); // أحمر حكومي
        default:
          return const Color(0xFF2D3748); // رمادي حكومي افتراضي
      }
    }

    Color getGovernmentBackgroundColor(String title) {
      switch (title.toLowerCase()) {
        case 'اليوم':
          return const Color(0xFFF0FDF4); // خلفية خضراء فاتحة
        case 'أمس':
          return const Color(0xFFF7FAFC); // خلفية رمادية فاتحة
        case 'هذا الأسبوع':
          return const Color(0xFFEBF8FF); // خلفية زرقاء فاتحة
        case 'مستحق اليوم':
          return const Color(0xFFFFFBEB); // خلفية ذهبية فاتحة
        case 'قريب الاستحقاق':
          return const Color(0xFFFFF5F5); // خلفية برتقالية فاتحة
        case 'متأخر':
          return const Color(0xFFFED7D7); // خلفية حمراء فاتحة
        default:
          return const Color(0xFFF7FAFC); // خلفية رمادية فاتحة افتراضية
      }
    }

    final governmentColor = getGovernmentColor(title);
    final backgroundColor = getGovernmentBackgroundColor(title);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            backgroundColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: governmentColor.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: governmentColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: عنوان البطاقة والعملية
          Row(
            children: [
              // أيقونة وعنوان البطاقة
              Expanded(
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: governmentColor,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: governmentColor.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        getIconForTitle(title),
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 13,
                          color: governmentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // العملية
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: governmentColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Text(
                  count,
                  style: TextStyle(
                    fontSize: 11,
                    color: governmentColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // الصف الثاني: المبلغ والقيمة
          Row(
            children: [
              // عنوان المبلغ
              Text(
                'المبلغ:',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(width: 8),

              // قيمة المبلغ
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: governmentColor.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Text(
                    amount,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: governmentColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة لتحديد الأيقونة المناسبة لنوع الكارت
  IconData _getCardTypeIcon(String cardType) {
    final lowerCardType = cardType.toLowerCase();

    if (lowerCardType.contains('شحن') || lowerCardType.contains('recharge')) {
      return Icons.battery_charging_full_rounded;
    } else if (lowerCardType.contains('انترنت') ||
        lowerCardType.contains('internet') ||
        lowerCardType.contains('data')) {
      return Icons.wifi_rounded;
    } else if (lowerCardType.contains('مكالمات') ||
        lowerCardType.contains('calls')) {
      return Icons.phone_rounded;
    } else if (lowerCardType.contains('نقدي') ||
        lowerCardType.contains('cash')) {
      return Icons.payments_rounded;
    } else if (lowerCardType.contains('فيزا') ||
        lowerCardType.contains('visa')) {
      return Icons.credit_card_rounded;
    } else if (lowerCardType.contains('ماستركارد') ||
        lowerCardType.contains('mastercard')) {
      return Icons.credit_card_rounded;
    } else if (lowerCardType.contains('مدى') ||
        lowerCardType.contains('mada')) {
      return Icons.account_balance_wallet_rounded;
    } else if (lowerCardType.contains('زين') || lowerCardType.contains('zين')) {
      return Icons.sim_card_rounded;
    } else if (lowerCardType.contains('stc')) {
      return Icons.sim_card_rounded;
    } else if (lowerCardType.contains('موبايلي') ||
        lowerCardType.contains('mobily')) {
      return Icons.sim_card_rounded;
    } else if (lowerCardType.contains('سوا') ||
        lowerCardType.contains('sawa')) {
      return Icons.sim_card_rounded;
    } else if (lowerCardType.contains('جوي') ||
        lowerCardType.contains('jawwy')) {
      return Icons.sim_card_rounded;
    } else {
      return Icons.credit_card_rounded; // أيقونة افتراضية
    }
  }

  // دالة لتحديد اللون المناسب لنوع الكارت
  Color _getCardTypeColor(String cardType) {
    final lowerCardType = cardType.toLowerCase();

    if (lowerCardType.contains('شحن') || lowerCardType.contains('recharge')) {
      return Colors.green; // أخضر للشحن
    } else if (lowerCardType.contains('انترنت') ||
        lowerCardType.contains('internet') ||
        lowerCardType.contains('data')) {
      return Colors.blue; // أزرق للانترنت
    } else if (lowerCardType.contains('مكالمات') ||
        lowerCardType.contains('calls')) {
      return Colors.orange; // برتقالي للمكالمات
    } else if (lowerCardType.contains('نقدي') ||
        lowerCardType.contains('cash')) {
      return Colors.amber; // ذهبي للنقدي
    } else if (lowerCardType.contains('فيزا') ||
        lowerCardType.contains('visa')) {
      return Colors.indigo; // نيلي للفيزا
    } else if (lowerCardType.contains('ماستركارد') ||
        lowerCardType.contains('mastercard')) {
      return Colors.red; // أحمر للماستركارد
    } else if (lowerCardType.contains('مدى') ||
        lowerCardType.contains('mada')) {
      return Colors.teal; // فيروزي للمدى
    } else if (lowerCardType.contains('زين') ||
        lowerCardType.contains('zain')) {
      return Colors.purple; // بنفسجي لزين
    } else if (lowerCardType.contains('stc')) {
      return Colors.blue; // أزرق لـ STC
    } else if (lowerCardType.contains('موبايلي') ||
        lowerCardType.contains('mobily')) {
      return Colors.green; // أخضر لموبايلي
    } else if (lowerCardType.contains('سوا') ||
        lowerCardType.contains('sawa')) {
      return Colors.orange; // برتقالي لسوا
    } else if (lowerCardType.contains('جوي') ||
        lowerCardType.contains('jawwy')) {
      return Colors.pink; // وردي لجوي
    } else {
      return Colors.grey.shade600; // رمادي افتراضي
    }
  }

  // دالة لتنسيق مبلغ الديون بأرقام عشرية وفاصل الآلاف (بدون عملة)
  String _formatDebtAmountText(double amount) {
    if (amount == 0) {
      return '0.000';
    }

    // تنسيق الرقم بثلاث خانات عشرية وفاصل الآلاف بدون عملة
    final formatter = NumberFormat('#,##0.000', 'ar');
    return formatter.format(amount);
  }

  // دالة لتنسيق عدد الكارتات في الديون
  String _formatDebtCardsCountText(int count) {
    if (count == 0) {
      return 'لا توجد كارتات';
    } else if (count == 1) {
      return 'كارت واحد';
    } else if (count == 2) {
      return 'كارتان';
    } else if (count <= 10) {
      return '$count كارتات';
    } else if (count <= 100) {
      return '$count كارت';
    } else {
      return '+100 كارت';
    }
  }
}
