import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/notification_schedule.dart';
import 'local_notification_service.dart';

class NotificationSchedulerService {
  factory NotificationSchedulerService() => _instance;
  NotificationSchedulerService._internal();
  static final NotificationSchedulerService _instance =
      NotificationSchedulerService._internal();

  final LocalNotificationService _notificationService =
      LocalNotificationService();
  List<NotificationSchedule> _schedules = [];
  bool _isInitialized = false;

  // تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadSchedules();
    await _scheduleAllNotifications();
    _isInitialized = true;

    debugPrint('✅ تم تهيئة خدمة جدولة التنبيهات');
  }

  // تحميل الجدولة المحفوظة
  Future<void> _loadSchedules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final schedulesJson = prefs.getString('notification_schedules');

      if (schedulesJson != null) {
        final List<dynamic> schedulesList = json.decode(schedulesJson);
        _schedules = schedulesList
            .map((json) => NotificationSchedule.fromJson(json))
            .toList();
      } else {
        // إنشاء جدولة افتراضية
        _schedules = _createDefaultSchedules();
        await _saveSchedules();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الجدولة: $e');
      _schedules = _createDefaultSchedules();
    }
  }

  // حفظ الجدولة
  Future<void> _saveSchedules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final schedulesJson =
          json.encode(_schedules.map((s) => s.toJson()).toList());
      await prefs.setString('notification_schedules', schedulesJson);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الجدولة: $e');
    }
  }

  // إنشاء جدولة افتراضية
  List<NotificationSchedule> _createDefaultSchedules() {
    return [
      NotificationSchedule(
        id: 1,
        name: 'تنبيه الصباح',
        time: const TimeOfDay(hour: 9, minute: 0),
        weekdays: [1, 2, 3, 4, 5, 6, 7], // كل يوم
        isEnabled: true,
        type: NotificationScheduleType.summary,
      ),
      NotificationSchedule(
        id: 2,
        name: 'تنبيه المساء',
        time: const TimeOfDay(hour: 18, minute: 0),
        weekdays: [1, 2, 3, 4, 5, 6, 7], // كل يوم
        isEnabled: false,
        type: NotificationScheduleType.overdue,
      ),
    ];
  }

  // الحصول على جميع الجدولة
  List<NotificationSchedule> get schedules => List.unmodifiable(_schedules);

  // إضافة جدولة جديدة
  Future<void> addSchedule(NotificationSchedule schedule) async {
    // التأكد من عدم تكرار المعرف
    final newId = _schedules.isEmpty
        ? 1
        : _schedules.map((s) => s.id).reduce((a, b) => a > b ? a : b) + 1;
    final newSchedule = schedule.copyWith(id: newId);

    _schedules.add(newSchedule);
    await _saveSchedules();

    if (newSchedule.isEnabled) {
      await _scheduleNotification(newSchedule);
    }

    debugPrint('✅ تم إضافة جدولة جديدة: ${newSchedule.name}');
  }

  // تحديث جدولة موجودة
  Future<void> updateSchedule(NotificationSchedule schedule) async {
    final index = _schedules.indexWhere((s) => s.id == schedule.id);
    if (index != -1) {
      // إلغاء الجدولة القديمة
      await _cancelNotification(schedule.id);

      _schedules[index] = schedule;
      await _saveSchedules();

      if (schedule.isEnabled) {
        await _scheduleNotification(schedule);
      }

      debugPrint('✅ تم تحديث الجدولة: ${schedule.name}');
    }
  }

  // حذف جدولة
  Future<void> deleteSchedule(int id) async {
    await _cancelNotification(id);
    _schedules.removeWhere((s) => s.id == id);
    await _saveSchedules();

    debugPrint('✅ تم حذف الجدولة: $id');
  }

  // تفعيل/إلغاء تفعيل جدولة
  Future<void> toggleSchedule(int id, bool enabled) async {
    final index = _schedules.indexWhere((s) => s.id == id);
    if (index != -1) {
      final schedule = _schedules[index].copyWith(isEnabled: enabled);
      await updateSchedule(schedule);
    }
  }

  // جدولة جميع التنبيهات المفعلة
  Future<void> _scheduleAllNotifications() async {
    // إلغاء جميع التنبيهات المجدولة أولاً
    await _cancelAllNotifications();

    // جدولة التنبيهات المفعلة
    for (final schedule in _schedules.where((s) => s.isEnabled)) {
      await _scheduleNotification(schedule);
    }

    debugPrint(
        '✅ تم جدولة ${_schedules.where((s) => s.isEnabled).length} تنبيه');
  }

  // جدولة تنبيه واحد
  Future<void> _scheduleNotification(NotificationSchedule schedule) async {
    try {
      final now = DateTime.now();
      var scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        schedule.time.hour,
        schedule.time.minute,
      );

      // إذا كان الوقت قد مضى اليوم، جدوله للغد
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }

      String title;
      String body;

      switch (schedule.type) {
        case NotificationScheduleType.summary:
          title = '📊 ملخص يومي';
          body = 'تحقق من حالة الديون والمبيعات اليوم';
          break;
        case NotificationScheduleType.overdue:
          title = '⚠️ تنبيه الديون المتأخرة';
          body = 'لديك ديون متأخرة تحتاج متابعة';
          break;
        case NotificationScheduleType.dueToday:
          title = '📅 ديون مستحقة اليوم';
          body = 'تحقق من الديون المستحقة اليوم';
          break;
        case NotificationScheduleType.dueSoon:
          title = '⏰ ديون مستحقة قريباً';
          body = 'لديك ديون ستستحق خلال الأيام القادمة';
          break;
        case NotificationScheduleType.custom:
          title = schedule.name;
          body = schedule.customMessage ?? 'تنبيه مخصص';
          break;
        default:
          title = schedule.name;
          body = 'تنبيه يومي';
      }

      await _notificationService.scheduleNotification(
        id: schedule.id,
        title: title,
        body: body,
        scheduledTime: scheduledTime,
      );

      debugPrint(
          '✅ تم جدولة ${schedule.name} في ${schedule.time.hour}:${schedule.time.minute.toString().padLeft(2, '0')}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة ${schedule.name}: $e');
    }
  }

  // إلغاء تنبيه واحد
  Future<void> _cancelNotification(int id) async {
    try {
      await _notificationService.cancelNotification(id);
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء التنبيه $id: $e');
    }
  }

  // إلغاء جميع التنبيهات
  Future<void> _cancelAllNotifications() async {
    try {
      for (final schedule in _schedules) {
        await _cancelNotification(schedule.id);
      }
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء جميع التنبيهات: $e');
    }
  }

  // إعادة جدولة جميع التنبيهات
  Future<void> rescheduleAll() async {
    await _scheduleAllNotifications();
  }

  // تنظيف الخدمة
  Future<void> dispose() async {
    await _cancelAllNotifications();
    _isInitialized = false;
  }
}
