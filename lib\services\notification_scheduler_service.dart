import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/notification_schedule.dart';
import '../database/database_helper.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import 'local_notification_service.dart';

class NotificationSchedulerService {
  factory NotificationSchedulerService() => _instance;
  NotificationSchedulerService._internal();
  static final NotificationSchedulerService _instance =
      NotificationSchedulerService._internal();

  final LocalNotificationService _notificationService =
      LocalNotificationService();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<NotificationSchedule> _schedules = [];
  bool _isInitialized = false;

  // تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadSchedules();
    await _scheduleAllNotifications();
    _isInitialized = true;

    debugPrint('✅ تم تهيئة خدمة جدولة التنبيهات');
  }

  // تحميل الجدولة المحفوظة
  Future<void> _loadSchedules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final schedulesJson = prefs.getString('notification_schedules');

      if (schedulesJson != null) {
        final List<dynamic> schedulesList = json.decode(schedulesJson);
        _schedules = schedulesList
            .map((json) => NotificationSchedule.fromJson(json))
            .toList();
      } else {
        // إنشاء جدولة افتراضية
        _schedules = _createDefaultSchedules();
        await _saveSchedules();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الجدولة: $e');
      _schedules = _createDefaultSchedules();
    }
  }

  // حفظ الجدولة
  Future<void> _saveSchedules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final schedulesJson =
          json.encode(_schedules.map((s) => s.toJson()).toList());
      await prefs.setString('notification_schedules', schedulesJson);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الجدولة: $e');
    }
  }

  // إنشاء جدولة افتراضية
  List<NotificationSchedule> _createDefaultSchedules() {
    return [
      NotificationSchedule(
        id: 1,
        name: 'تنبيه الصباح',
        time: const TimeOfDay(hour: 9, minute: 0),
        weekdays: [1, 2, 3, 4, 5, 6, 7], // كل يوم
        isEnabled: true,
        type: NotificationScheduleType.summary,
      ),
      NotificationSchedule(
        id: 2,
        name: 'تنبيه المساء',
        time: const TimeOfDay(hour: 18, minute: 0),
        weekdays: [1, 2, 3, 4, 5, 6, 7], // كل يوم
        isEnabled: false,
        type: NotificationScheduleType.overdue,
      ),
    ];
  }

  // الحصول على جميع الجدولة
  List<NotificationSchedule> get schedules => List.unmodifiable(_schedules);

  // إضافة جدولة جديدة
  Future<void> addSchedule(NotificationSchedule schedule) async {
    // التأكد من عدم تكرار المعرف
    final newId = _schedules.isEmpty
        ? 1
        : _schedules.map((s) => s.id).reduce((a, b) => a > b ? a : b) + 1;
    final newSchedule = schedule.copyWith(id: newId);

    _schedules.add(newSchedule);
    await _saveSchedules();

    if (newSchedule.isEnabled) {
      await _scheduleNotification(newSchedule);
    }

    debugPrint('✅ تم إضافة جدولة جديدة: ${newSchedule.name}');
  }

  // تحديث جدولة موجودة
  Future<void> updateSchedule(NotificationSchedule schedule) async {
    final index = _schedules.indexWhere((s) => s.id == schedule.id);
    if (index != -1) {
      // إلغاء الجدولة القديمة
      await _cancelNotification(schedule.id);

      _schedules[index] = schedule;
      await _saveSchedules();

      if (schedule.isEnabled) {
        await _scheduleNotification(schedule);
      }

      debugPrint('✅ تم تحديث الجدولة: ${schedule.name}');
    }
  }

  // حذف جدولة
  Future<void> deleteSchedule(int id) async {
    await _cancelNotification(id);
    _schedules.removeWhere((s) => s.id == id);
    await _saveSchedules();

    debugPrint('✅ تم حذف الجدولة: $id');
  }

  // تفعيل/إلغاء تفعيل جدولة
  Future<void> toggleSchedule(int id, bool enabled) async {
    final index = _schedules.indexWhere((s) => s.id == id);
    if (index != -1) {
      final schedule = _schedules[index].copyWith(isEnabled: enabled);
      await updateSchedule(schedule);
    }
  }

  // جدولة جميع التنبيهات المفعلة
  Future<void> _scheduleAllNotifications() async {
    // إلغاء جميع التنبيهات المجدولة أولاً
    await _cancelAllNotifications();

    // جدولة التنبيهات المفعلة
    for (final schedule in _schedules.where((s) => s.isEnabled)) {
      await _scheduleNotification(schedule);
    }

    debugPrint(
        '✅ تم جدولة ${_schedules.where((s) => s.isEnabled).length} تنبيه');
  }

  // جدولة تنبيه واحد
  Future<void> _scheduleNotification(NotificationSchedule schedule) async {
    try {
      // جدولة التنبيه لكل يوم من الأيام المحددة
      for (final weekday in schedule.weekdays) {
        await _scheduleNotificationForWeekday(schedule, weekday);
      }

      debugPrint(
          '✅ تم جدولة ${schedule.name} في ${schedule.time.hour}:${schedule.time.minute.toString().padLeft(2, '0')} للأيام: ${schedule.weekdays.join(', ')}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة ${schedule.name}: $e');
    }
  }

  // جدولة تنبيه ليوم محدد من الأسبوع
  Future<void> _scheduleNotificationForWeekday(
    NotificationSchedule schedule,
    int weekday,
  ) async {
    try {
      final now = DateTime.now();

      // العثور على التاريخ التالي لهذا اليوم من الأسبوع
      var scheduledTime = _getNextWeekdayDateTime(
        weekday,
        schedule.time.hour,
        schedule.time.minute,
      );

      // إذا كان الوقت اليوم ولم يمر بعد، استخدمه
      if (scheduledTime.weekday == weekday &&
          scheduledTime.isAfter(now) &&
          scheduledTime.day == now.day) {
        // الوقت اليوم ولم يمر بعد
      } else if (scheduledTime.isBefore(now) ||
          scheduledTime.weekday != weekday) {
        // البحث عن التاريخ التالي لهذا اليوم
        scheduledTime = _getNextWeekdayDateTime(
          weekday,
          schedule.time.hour,
          schedule.time.minute,
        );
      }

      // الحصول على بيانات الديون الفعلية
      final debtsData = await _getActualDebtsData();

      String title;
      String body;

      switch (schedule.type) {
        case NotificationScheduleType.summary:
          title = '📊 ملخص يومي';
          body = _buildSummaryNotificationBody(debtsData);
          break;
        case NotificationScheduleType.overdue:
          title = '⚠️ ديون متأخرة (${debtsData['overdueCount']})';
          body = _buildOverdueNotificationBody(debtsData);
          break;
        case NotificationScheduleType.dueToday:
          title = '📅 ديون مستحقة اليوم (${debtsData['dueTodayCount']})';
          body = _buildDueTodayNotificationBody(debtsData);
          break;
        case NotificationScheduleType.dueSoon:
          title = '⏰ ديون مستحقة قريباً (${debtsData['dueSoonCount']})';
          body = _buildDueSoonNotificationBody(debtsData);
          break;
        case NotificationScheduleType.custom:
          title = schedule.name;
          body = schedule.customMessage ?? 'تنبيه مخصص';
          break;
        default:
          title = schedule.name;
          body = 'تنبيه يومي';
      }

      // استخدام معرف فريد لكل يوم
      final uniqueId = schedule.id * 10 + weekday;

      await _notificationService.scheduleWeeklyNotification(
        id: uniqueId,
        title: title,
        body: body,
        scheduledTime: scheduledTime,
        weekday: weekday,
      );

      debugPrint(
          '✅ تم جدولة ${schedule.name} ليوم ${_getDayName(weekday)} في ${schedule.time.hour}:${schedule.time.minute.toString().padLeft(2, '0')}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة ${schedule.name} ليوم $weekday: $e');
    }
  }

  // الحصول على التاريخ التالي ليوم محدد من الأسبوع
  DateTime _getNextWeekdayDateTime(int targetWeekday, int hour, int minute) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day, hour, minute);

    // إذا كان اليوم هو اليوم المطلوب والوقت لم يمر بعد
    if (now.weekday == targetWeekday && today.isAfter(now)) {
      return today;
    }

    // البحث عن اليوم التالي
    var nextDate = DateTime(now.year, now.month, now.day, hour, minute);

    // إضافة أيام حتى نصل لليوم المطلوب
    while (nextDate.weekday != targetWeekday || nextDate.isBefore(now)) {
      nextDate = nextDate.add(const Duration(days: 1));
    }

    return nextDate;
  }

  // الحصول على اسم اليوم
  String _getDayName(int weekday) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return dayNames[weekday - 1];
  }

  // إلغاء تنبيه واحد (جميع أيام الأسبوع)
  Future<void> _cancelNotification(int scheduleId) async {
    try {
      // إلغاء التنبيه لكل يوم من أيام الأسبوع
      for (int weekday = 1; weekday <= 7; weekday++) {
        final uniqueId = scheduleId * 10 + weekday;
        await _notificationService.cancelNotification(uniqueId);
      }
      debugPrint('✅ تم إلغاء جميع تنبيهات الجدولة $scheduleId');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء التنبيه $scheduleId: $e');
    }
  }

  // إلغاء جميع التنبيهات
  Future<void> _cancelAllNotifications() async {
    try {
      for (final schedule in _schedules) {
        await _cancelNotification(schedule.id);
      }
      debugPrint('✅ تم إلغاء جميع التنبيهات المجدولة');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء جميع التنبيهات: $e');
    }
  }

  // إعادة جدولة جميع التنبيهات
  Future<void> rescheduleAll() async {
    await _scheduleAllNotifications();
  }

  // اختبار التنبيهات المجدولة - إرسال تنبيه فوري لكل جدولة مفعلة
  Future<void> testScheduledNotifications() async {
    try {
      final enabledSchedules = _schedules.where((s) => s.isEnabled).toList();

      if (enabledSchedules.isEmpty) {
        debugPrint('⚠️ لا توجد تنبيهات مجدولة مفعلة للاختبار');
        return;
      }

      for (final schedule in enabledSchedules) {
        String title;
        String body;

        switch (schedule.type) {
          case NotificationScheduleType.summary:
            title = '🧪 اختبار: ملخص يومي';
            body = 'هذا اختبار للتنبيه المجدول - تحقق من حالة الديون والمبيعات';
            break;
          case NotificationScheduleType.overdue:
            title = '🧪 اختبار: تنبيه الديون المتأخرة';
            body = 'هذا اختبار للتنبيه المجدول - لديك ديون متأخرة تحتاج متابعة';
            break;
          case NotificationScheduleType.dueToday:
            title = '🧪 اختبار: ديون مستحقة اليوم';
            body = 'هذا اختبار للتنبيه المجدول - تحقق من الديون المستحقة اليوم';
            break;
          case NotificationScheduleType.dueSoon:
            title = '🧪 اختبار: ديون مستحقة قريباً';
            body =
                'هذا اختبار للتنبيه المجدول - لديك ديون ستستحق خلال الأيام القادمة';
            break;
          case NotificationScheduleType.custom:
            title = '🧪 اختبار: ${schedule.name}';
            body =
                'هذا اختبار للتنبيه المجدول - ${schedule.customMessage ?? 'تنبيه مخصص'}';
            break;
          default:
            title = '🧪 اختبار: ${schedule.name}';
            body = 'هذا اختبار للتنبيه المجدول - تنبيه يومي';
        }

        // إرسال تنبيه فوري للاختبار
        await _notificationService.showTestNotification(
          id: 9000 + schedule.id,
          title: title,
          body:
              '$body\n\nمجدول للساعة ${schedule.time.hour}:${schedule.time.minute.toString().padLeft(2, '0')} في أيام: ${schedule.weekdays.map(_getDayName).join(', ')}',
        );

        // تأخير قصير بين التنبيهات
        await Future.delayed(const Duration(milliseconds: 500));
      }

      debugPrint('✅ تم إرسال ${enabledSchedules.length} تنبيه اختبار');
    } catch (e) {
      debugPrint('❌ خطأ في اختبار التنبيهات المجدولة: $e');
    }
  }

  // الحصول على بيانات الديون الفعلية
  Future<Map<String, dynamic>> _getActualDebtsData() async {
    try {
      final allDebts = await _databaseHelper.getAllDebts();
      final allCustomers = await _databaseHelper.getAllCustomers();

      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final nextWeek = today.add(const Duration(days: 7));

      // فلترة الديون غير المسددة
      final unpaidDebts =
          allDebts.where((debt) => debt.remainingAmount > 0).toList();

      // الديون المتأخرة
      final overdueDebts = unpaidDebts.where((debt) {
        final dueDate =
            DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
        return dueDate.isBefore(today);
      }).toList();

      // الديون المستحقة اليوم
      final dueTodayDebts = unpaidDebts.where((debt) {
        final dueDate =
            DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
        return dueDate.isAtSameMomentAs(today);
      }).toList();

      // الديون المستحقة قريباً (خلال أسبوع)
      final dueSoonDebts = unpaidDebts.where((debt) {
        final dueDate =
            DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
        return dueDate.isAfter(today) && dueDate.isBefore(nextWeek);
      }).toList();

      // حساب المبالغ
      final overdueAmount = overdueDebts.fold<double>(
          0, (sum, debt) => sum + debt.remainingAmount);
      final dueTodayAmount = dueTodayDebts.fold<double>(
          0, (sum, debt) => sum + debt.remainingAmount);
      final dueSoonAmount = dueSoonDebts.fold<double>(
          0, (sum, debt) => sum + debt.remainingAmount);

      // تجميع العملاء
      final overdueCustomers =
          _groupDebtsByCustomer(overdueDebts, allCustomers);
      final dueTodayCustomers =
          _groupDebtsByCustomer(dueTodayDebts, allCustomers);
      final dueSoonCustomers =
          _groupDebtsByCustomer(dueSoonDebts, allCustomers);

      return {
        'overdueCount': overdueDebts.length,
        'overdueAmount': overdueAmount,
        'overdueCustomers': overdueCustomers,
        'dueTodayCount': dueTodayDebts.length,
        'dueTodayAmount': dueTodayAmount,
        'dueTodayCustomers': dueTodayCustomers,
        'dueSoonCount': dueSoonDebts.length,
        'dueSoonAmount': dueSoonAmount,
        'dueSoonCustomers': dueSoonCustomers,
        'totalUnpaidCount': unpaidDebts.length,
        'totalUnpaidAmount': unpaidDebts.fold<double>(
            0, (sum, debt) => sum + debt.remainingAmount),
      };
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الديون: $e');
      return {
        'overdueCount': 0,
        'overdueAmount': 0.0,
        'overdueCustomers': <Map<String, dynamic>>[],
        'dueTodayCount': 0,
        'dueTodayAmount': 0.0,
        'dueTodayCustomers': <Map<String, dynamic>>[],
        'dueSoonCount': 0,
        'dueSoonAmount': 0.0,
        'dueSoonCustomers': <Map<String, dynamic>>[],
        'totalUnpaidCount': 0,
        'totalUnpaidAmount': 0.0,
      };
    }
  }

  // تجميع الديون حسب العميل
  List<Map<String, dynamic>> _groupDebtsByCustomer(
      List<Debt> debts, List<Customer> customers) {
    final Map<int, Map<String, dynamic>> customerGroups = {};

    for (final debt in debts) {
      final customer = customers.firstWhere(
        (c) => c.id == debt.customerId,
        orElse: () => Customer(
          id: debt.customerId,
          name: 'عميل غير معروف',
          phone: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      if (!customerGroups.containsKey(debt.customerId)) {
        customerGroups[debt.customerId] = {
          'customerId': debt.customerId,
          'customerName': customer.name,
          'customerPhone': customer.phone,
          'debts': <Debt>[],
          'totalAmount': 0.0,
          'debtCount': 0,
        };
      }

      customerGroups[debt.customerId]!['debts'].add(debt);
      customerGroups[debt.customerId]!['totalAmount'] += debt.remainingAmount;
      customerGroups[debt.customerId]!['debtCount']++;
    }

    return customerGroups.values.toList()
      ..sort((a, b) =>
          (b['totalAmount'] as double).compareTo(a['totalAmount'] as double));
  }

  // بناء محتوى تنبيه الملخص
  String _buildSummaryNotificationBody(Map<String, dynamic> data) {
    final overdueCount = data['overdueCount'] as int;
    final dueTodayCount = data['dueTodayCount'] as int;
    final dueSoonCount = data['dueSoonCount'] as int;
    final totalAmount = data['totalUnpaidAmount'] as double;

    if (overdueCount == 0 && dueTodayCount == 0 && dueSoonCount == 0) {
      return '✅ لا توجد ديون مستحقة اليوم أو متأخرة';
    }

    final List<String> parts = [];

    if (overdueCount > 0) {
      parts.add('🔴 $overdueCount متأخر');
    }
    if (dueTodayCount > 0) {
      parts.add('🟠 $dueTodayCount اليوم');
    }
    if (dueSoonCount > 0) {
      parts.add('🟡 $dueSoonCount قريباً');
    }

    return '${parts.join(' • ')}\nإجمالي: ${_formatAmount(totalAmount)}';
  }

  // بناء محتوى تنبيه الديون المتأخرة
  String _buildOverdueNotificationBody(Map<String, dynamic> data) {
    final overdueCount = data['overdueCount'] as int;
    final overdueAmount = data['overdueAmount'] as double;
    final overdueCustomers =
        data['overdueCustomers'] as List<Map<String, dynamic>>;

    if (overdueCount == 0) {
      return '✅ لا توجد ديون متأخرة';
    }

    final topCustomers = overdueCustomers.take(3).map((customer) {
      return '• ${customer['customerName']} (${_formatAmount(customer['totalAmount'])})';
    }).join('\n');

    String body = 'إجمالي: ${_formatAmount(overdueAmount)}';
    if (topCustomers.isNotEmpty) {
      body += '\n\nأهم العملاء:\n$topCustomers';
    }
    if (overdueCustomers.length > 3) {
      body += '\nو ${overdueCustomers.length - 3} عملاء آخرين...';
    }

    return body;
  }

  // بناء محتوى تنبيه الديون المستحقة اليوم
  String _buildDueTodayNotificationBody(Map<String, dynamic> data) {
    final dueTodayCount = data['dueTodayCount'] as int;
    final dueTodayAmount = data['dueTodayAmount'] as double;
    final dueTodayCustomers =
        data['dueTodayCustomers'] as List<Map<String, dynamic>>;

    if (dueTodayCount == 0) {
      return '✅ لا توجد ديون مستحقة اليوم';
    }

    final topCustomers = dueTodayCustomers.take(3).map((customer) {
      return '• ${customer['customerName']} (${_formatAmount(customer['totalAmount'])})';
    }).join('\n');

    String body = 'إجمالي: ${_formatAmount(dueTodayAmount)}';
    if (topCustomers.isNotEmpty) {
      body += '\n\nالعملاء:\n$topCustomers';
    }
    if (dueTodayCustomers.length > 3) {
      body += '\nو ${dueTodayCustomers.length - 3} عملاء آخرين...';
    }

    return body;
  }

  // بناء محتوى تنبيه الديون المستحقة قريباً
  String _buildDueSoonNotificationBody(Map<String, dynamic> data) {
    final dueSoonCount = data['dueSoonCount'] as int;
    final dueSoonAmount = data['dueSoonAmount'] as double;
    final dueSoonCustomers =
        data['dueSoonCustomers'] as List<Map<String, dynamic>>;

    if (dueSoonCount == 0) {
      return '✅ لا توجد ديون مستحقة خلال الأسبوع القادم';
    }

    final topCustomers = dueSoonCustomers.take(3).map((customer) {
      return '• ${customer['customerName']} (${_formatAmount(customer['totalAmount'])})';
    }).join('\n');

    String body = 'خلال 7 أيام - إجمالي: ${_formatAmount(dueSoonAmount)}';
    if (topCustomers.isNotEmpty) {
      body += '\n\nالعملاء:\n$topCustomers';
    }
    if (dueSoonCustomers.length > 3) {
      body += '\nو ${dueSoonCustomers.length - 3} عملاء آخرين...';
    }

    return body;
  }

  // تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    }
    return amount.toStringAsFixed(0);
  }

  // تنظيف الخدمة
  Future<void> dispose() async {
    await _cancelAllNotifications();
    _isInitialized = false;
  }
}
